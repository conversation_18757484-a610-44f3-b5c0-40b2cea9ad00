/*!
 * @file
 * @brief Unified test runner for all test groups
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <stdarg.h>
#include <stddef.h>
#include <setjmp.h>
#include <cmocka.h>
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>

// External test group declarations
extern int run_utils_tests(void);
extern int run_tiny_linked_list_tests(void);
extern int run_queue_ring_buffer_tests(void);

int main(void)
{
    int result = 0;

    printf("=== Running All Test Groups ===\n\n");

    // Run Utils tests
    printf("--- Utils Tests ---\n");
    result += run_utils_tests();

    // Run TinyLinkedList tests
    printf("\n--- TinyLinkedList Tests ---\n");
    result += run_tiny_linked_list_tests();

    // Run Queue_RingBuffer tests
    printf("\n--- Queue_RingBuffer Tests ---\n");
    result += run_queue_ring_buffer_tests();

    printf("\n=== Test Summary ===\n");
    if(result == 0)
    {
        printf("All tests PASSED!\n");
    }
    else
    {
        printf("Total FAILED tests: %d\n", result);
    }

    return result;
}