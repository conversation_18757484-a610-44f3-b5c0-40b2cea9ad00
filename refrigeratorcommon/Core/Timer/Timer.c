/*!
 * @file
 * @brief Timer implementation.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <stdbool.h>
#include <stddef.h>
#include "Timer.h"
#include "LinkedList.h"

#define MAX_TICKS ((TimerTicks_t)(-1))

static void AddTimer(TimerModule_st *module, Timer_st *timer)
{
    TimerTicks_t expirationTicks = timer->ticks.expiration;
    size_t insertionIndex = LinkedList_Count(&module->timers);
    size_t currentIndex = 0;

    LinkedList_ForEach(&module->timers, Timer_st, _timer, {
        if(!(_timer->paused || _timer->expired))
        {
            TimerTicks_t remainingTicksForCurrentTimer = _timer->ticks.expiration - module->lastTicks;
            TimerTicks_t remainingTicksForTimerToInsert = expirationTicks - module->lastTicks;

            if(remainingTicksForCurrentTimer > remainingTicksForTimerToInsert)
            {
                insertionIndex = currentIndex;
                break;
            }
        }

        currentIndex++;
    });

    LinkedList_Insert(&module->timers, &timer->node, insertionIndex);
}

static void RemoveTimer(TimerModule_st *module, Timer_st *timer)
{
    LinkedList_Remove(&module->timers, &timer->node);
}

void TimerModule_Stop(TimerModule_st *module, Timer_st *timer)
{
    RemoveTimer(module, timer);
}

static TimerTicks_t PendingTicks(TimerModule_st *module)
{
    TimeSourceTickCount_t newTickCount = TimeSource_GetTicks(module->timeSource);
    return (TimerTicks_t)((TimeSourceTickCount_t)(newTickCount - module->lastTimeSourceTickCount));
}

static void StartTimer(TimerModule_st *module, Timer_st *timer, const TimerTicks_t ticks, pfnTimerCallback callback, void *context)
{
    TimerTicks_t timerDuration = TRUNCATE_UNSIGNED_ADDITION(ticks, PendingTicks(module), MAX_TICKS);

    TimerModule_Stop(module, timer);

    timer->startTicks = ticks;
    timer->ticks.expiration = module->lastTicks + timerDuration;
    timer->context = context;
    timer->callback = callback;
    timer->paused = false;
    timer->expired = false;

    AddTimer(module, timer);
}

void TimerModule_StartOneShot(TimerModule_st *module, Timer_st *timer, const TimerTicks_t ticks, pfnTimerCallback callback, void *context)
{
    timer->autoReload = false;
    StartTimer(module, timer, ticks, callback, context);
}

void TimerModule_StartPeriodic(TimerModule_st *module, Timer_st *timer, const TimerTicks_t ticks, pfnTimerCallback callback, void *context)
{
    timer->autoReload = true;
    StartTimer(module, timer, ticks, callback, context);
}

bool TimerModule_IsRunning(TimerModule_st *module, Timer_st *timer)
{
    return LinkedList_Contains(&module->timers, (const LinkedListNode_st *)timer);
}

void TimerModule_Pause(TimerModule_st *module, Timer_st *timer)
{
    if(TimerModule_IsRunning(module, timer))
    {
        timer->ticks.pause = TimerModule_RemainingTicks(module, timer);
        timer->paused = true;
    }
}

void TimerModule_Resume(TimerModule_st *module, Timer_st *timer)
{
    if(TimerModule_IsPaused(module, timer))
    {
        TimerTicks_t remainingTicks = TRUNCATE_UNSIGNED_ADDITION(timer->ticks.pause, PendingTicks(module), MAX_TICKS);
        timer->paused = false;

        RemoveTimer(module, timer);
        timer->ticks.expiration = module->lastTicks + remainingTicks;
        AddTimer(module, timer);
    }
}

bool TimerModule_IsPaused(TimerModule_st *module, Timer_st *timer)
{
    if(TimerModule_IsRunning(module, timer))
    {
        return timer->paused;
    }
    else
    {
        return false;
    }
}

TimerTicks_t TimerModule_StartTicks(TimerModule_st *module, Timer_st *timer)
{
    if(TimerModule_IsRunning(module, timer))
    {
        return timer->startTicks;
    }
    else
    {
        return 0;
    }
}

TimerTicks_t TimerModule_RemainingTicks(TimerModule_st *module, Timer_st *timer)
{
    if(TimerModule_IsRunning(module, timer))
    {
        if(TimerModule_IsPaused(module, timer))
        {
            return timer->ticks.pause;
        }
        else
        {
            TimerTicks_t expiration = timer->ticks.expiration;
            TimerTicks_t lastTicks = module->lastTicks;
            TimerTicks_t pending = PendingTicks(module);
            TimerTicks_t remainingWithoutPending = expiration - lastTicks;
            return TRUNCATE_UNSIGNED_SUBTRACTION(remainingWithoutPending, pending);
        }
    }
    else
    {
        return 0;
    }
}

TimerTicks_t TimerModule_TicksSinceLastStarted(TimerModule_st *module, Timer_st *timer)
{
    //ASSERT(!TimerModule_IsPaused(module, timer));
    return module->lastTicks - (timer->ticks.expiration - timer->startTicks) + PendingTicks(module);
}

TimerTicks_t TimerModule_ElapsedTicks(TimerModule_st *module, Timer_st *timer)
{
    if(TimerModule_IsRunning(module, timer))
    {
        return timer->startTicks - TimerModule_RemainingTicks(module, timer);
    }
    else
    {
        return 0;
    }
}

void TimerModule_Init(TimerModule_st *module, I_TimeSource_st *timeSource)
{
    module->timeSource = timeSource;
    module->lastTimeSourceTickCount = TimeSource_GetTicks(timeSource);
    LinkedList_Init(&module->timers);
}

static void ExecuteTimer(TimerModule_st *module, Timer_st *timer)
{
    if(!timer->autoReload)
    {
        TimerModule_Stop(module, timer);
    }

    timer->callback(timer->context);

    // If the timer was stopped in the callback we should not restart the timer
    if(TimerModule_IsRunning(module, timer) && timer->autoReload)
    {
        RemoveTimer(module, timer);
        timer->expired = false;
        timer->ticks.expiration = module->lastTicks + timer->startTicks + PendingTicks(module);
        AddTimer(module, timer);
    }
}

bool TimerModule_Run(TimerModule_st *module)
{
    {
        TimeSourceTickCount_t newTickCount = TimeSource_GetTicks(module->timeSource);
        TimerTicks_t deltaTicks = (TimerTicks_t)((TimeSourceTickCount_t)(newTickCount - module->lastTimeSourceTickCount));
        TimerTicks_t newTicks = module->lastTicks + deltaTicks;

        TimerTicks_t lastTicks = module->lastTicks;

        module->lastTicks = newTicks;
        module->lastTimeSourceTickCount = newTickCount;

        LinkedList_ForEach(&module->timers, Timer_st, timer, {
            if(!timer->paused)
            {
                TimerTicks_t elapsedTicks = newTicks - lastTicks;
                TimerTicks_t remainingTicksForTimer = timer->ticks.expiration - lastTicks;

                if(elapsedTicks >= remainingTicksForTimer)
                {
                    timer->expired = true;
                }
            }

            if(!timer->expired && !timer->paused)
            {
                break;
            }
        });
    }

    LinkedList_ForEach(&module->timers, Timer_st, timer, {
        if(!timer->paused && timer->expired)
        {
            ExecuteTimer(module, timer);
            return true;
        }
    });

    return false;
}
