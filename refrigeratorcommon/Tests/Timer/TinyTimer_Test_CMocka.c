/*!
 * @file
 * @brief TinyTimer tests using CMocka
 *
 * Copyright xxxx - Confidential - All rights reserved.
 */

#include <stdarg.h>
#include <stddef.h>
#include <setjmp.h>
#include <cmocka.h>
#include <stdint.h>
#include <stdbool.h>

#include "TinyTimer.h"
#include "Adpt_TimeBase.h"
#include "TinyTimeSource_TestDouble.h"

static TinyTimerTicks_t restartTicks;
static TinyTimerModule_t timerModule;
static TinyTimer_t timer1;
static TinyTimer_t timer2;
static TinyTimer_t timer3;
static TinyTimer_t timerWithRestart;
static uint8_t context1;
static uint8_t context2;
static uint8_t context3;
static bool timerModuleCalledBackTimer;
static TinyTimeSource_TestDouble_t timeSource;

static void Callback1(void *context, TinyTimerModule_t *timerModule) {
    function_called();
    // Only check context if we have expectations set
    if (context && mock_type(int)) {
        check_expected_ptr(context);
    }
}

static void Callback2(void *context, TinyTimerModule_t *timerModule) {
    function_called();
    if (context && mock_type(int)) {
        check_expected_ptr(context);
    }
}

static void Callback3(void *context, TinyTimerModule_t *timerModule) {
    function_called();
    if (context && mock_type(int)) {
        check_expected_ptr(context);
    }
}

static void CallbackWithRestart(void *context, TinyTimerModule_t *timerModule) {
    function_called();
    TinyTimerModule_StartOneShot(timerModule, (TinyTimer_t *)context, restartTicks,
                          CallbackWithRestart, context);
}

static int setup(void **state) {
    TinyTimeSource_TestDouble_Init(&timeSource);
    TinyTimeSource_TestDouble_SetTicks(&timeSource, 0x1234);
    TinyTimerModule_Init(&timerModule, &timeSource.interface);
    return 0;
}

static void After(TinyTimerTicks_t ticks) {
    TinyTimeSource_TestDouble_TickMany(&timeSource, ticks);
}

static void AfterStartingTimer(TinyTimer_t *timer, TinyTimerTicks_t ticks) {
    TinyTimerCallback_t callback = NULL;
    void *context = NULL;

    if (timer == &timer1) {
        callback = Callback1;
        context = &context1;
    } else if (timer == &timer2) {
        callback = Callback2;
        context = &context2;
    } else if (timer == &timer3) {
        callback = Callback3;
        context = &context3;
    } else if (timer == &timerWithRestart) {
        callback = CallbackWithRestart;
        restartTicks = ticks;
        context = &timerWithRestart;
    }

    TinyTimerModule_StartOneShot(&timerModule, timer, ticks, callback, context);
}

static void GivenThatTimerHasBeenStarted(TinyTimer_t *timer, TinyTimerTicks_t ticks) {
    AfterStartingTimer(timer, ticks);
}

static void TimerCallbackShouldBeInvoked(TinyTimer_t *timer) {
    if (timer == &timer1) {
        expect_function_call(Callback1);
        will_return(Callback1, 1); // Enable context check
        expect_value(Callback1, context, &context1);
    } else if (timer == &timer2) {
        expect_function_call(Callback2);
        will_return(Callback2, 1); // Enable context check
        expect_value(Callback2, context, &context2);
    } else if (timer == &timer3) {
        expect_function_call(Callback3);
        will_return(Callback3, 1); // Enable context check
        expect_value(Callback3, context, &context3);
    } else if (timer == &timerWithRestart) {
        expect_function_call(CallbackWithRestart);
    }
}

static void TimerCallbackShouldBeInvokedAfterExactly(TinyTimer_t *timer, TinyTimerTicks_t ticks) {
    After(ticks - 1);
    timerModuleCalledBackTimer = TinyTimerModule_Run(&timerModule);

    TimerCallbackShouldBeInvoked(timer);
    After(1);
    timerModuleCalledBackTimer = TinyTimerModule_Run(&timerModule);
}

static void AfterTheTimerModuleIsRun(void) {
    timerModuleCalledBackTimer = TinyTimerModule_Run(&timerModule);
}

static void AfterTimePassesAndTheTimerModuleIsRun(TinyTimerTicks_t ticks) {
    After(ticks);
    AfterTheTimerModuleIsRun();
}

static void GivenThatTimeHasPassedAndTheTimerModuleHasBeenRun(TinyTimerTicks_t ticks) {
    AfterTimePassesAndTheTimerModuleIsRun(ticks);
}

static void AfterTimerIsStopped(TinyTimer_t *timer) {
    TinyTimerModule_Stop(&timerModule, timer);
}

static void RemainingTicksForTimerShouldBe(TinyTimer_t *timer, TinyTimerTicks_t ticks) {
    assert_int_equal(ticks, TinyTimerModule_RemainingTicks(&timerModule, timer));
}

static void TheTimerModuleShouldIndicateThatItDidNotCallBackAnyTimersOnTheLastRun(void) {
    assert_false(timerModuleCalledBackTimer);
}

static void TheTimerModuleShouldIndicateThatItCalledBackATimersOnTheLastRun(void) {
    assert_true(timerModuleCalledBackTimer);
}

// Test functions
static void test_ShouldInvokeTimerCallbackOnExpiration(void **state) {
    GivenThatTimerHasBeenStarted(&timer1, 5);
    TimerCallbackShouldBeInvokedAfterExactly(&timer1, 5);
}

static void test_ShouldNotInvokeTimerCallbackAgainAfterTimerHasAlreadyExpired(void **state) {
     GivenThatTimerHasBeenStarted(&timer1, 3);
    
    // First expiration - expect callback
    TimerCallbackShouldBeInvokedAfterExactly(&timer1, 3);
    
    // After expiration, no more callbacks should occur
    AfterTimePassesAndTheTimerModuleIsRun(0xFFFF);
    TheTimerModuleShouldIndicateThatItDidNotCallBackAnyTimersOnTheLastRun();
}

static void test_ShouldAllowARunningTimerToBeRestarted(void **state) {
    GivenThatTimerHasBeenStarted(&timer1, 5);
    GivenThatTimerHasBeenStarted(&timer1, 3);
    TimerCallbackShouldBeInvokedAfterExactly(&timer1, 3);
    
    AfterTimePassesAndTheTimerModuleIsRun(0xFFFF);
}

static void test_ShouldBeAbleToWatchMultipleTimers(void **state) {
    GivenThatTimerHasBeenStarted(&timer1, 3);
    GivenThatTimerHasBeenStarted(&timer2, 5);
    GivenThatTimerHasBeenStarted(&timer3, 7);

    TimerCallbackShouldBeInvokedAfterExactly(&timer1, 3);
    TimerCallbackShouldBeInvokedAfterExactly(&timer2, 2);
    TimerCallbackShouldBeInvokedAfterExactly(&timer3, 2);
}

static void test_ShouldAllowATimerToBeStoppedPriorToExpiration(void **state) {
    GivenThatTimerHasBeenStarted(&timer1, 5);
    
    GivenThatTimeHasPassedAndTheTimerModuleHasBeenRun(4);
    AfterTimerIsStopped(&timer1);
    AfterTimePassesAndTheTimerModuleIsRun(1);
}

static void test_ShouldExposeTheRemainingTicksForATimer(void **state) {
    GivenThatTimerHasBeenStarted(&timer1, 5);
    RemainingTicksForTimerShouldBe(&timer1, 5);
    
    AfterTimePassesAndTheTimerModuleIsRun(3);
    RemainingTicksForTimerShouldBe(&timer1, 2);
}

static void test_ShouldInvokeMultipleTimersPerRun(void **state) {
    GivenThatTimerHasBeenStarted(&timer1, 3);
    GivenThatTimerHasBeenStarted(&timer2, 3);
    
    // Use ignore_function_calls to avoid order dependency
    ignore_function_calls(Callback1);
    ignore_function_calls(Callback2);
    will_return_always(Callback1, 0); // Disable context check
    will_return_always(Callback2, 0); // Disable context check
    
    AfterTimePassesAndTheTimerModuleIsRun(3);
    TheTimerModuleShouldIndicateThatItCalledBackATimersOnTheLastRun();
}

static void test_ShouldIndicateWhenRunCausesATimerToBeCalledBack(void **state) {
    GivenThatTimerHasBeenStarted(&timer1, 3);
    
    AfterTimePassesAndTheTimerModuleIsRun(1);
    TheTimerModuleShouldIndicateThatItDidNotCallBackAnyTimersOnTheLastRun();
    
    AfterTimePassesAndTheTimerModuleIsRun(1);
    TheTimerModuleShouldIndicateThatItDidNotCallBackAnyTimersOnTheLastRun();
    
    TimerCallbackShouldBeInvoked(&timer1);
    AfterTimePassesAndTheTimerModuleIsRun(1);
    TheTimerModuleShouldIndicateThatItCalledBackATimersOnTheLastRun();
}

static void test_ShouldAllowATimerToBeRestartedFromACallback(void **state) {
    GivenThatTimerHasBeenStarted(&timerWithRestart, 3);
    
    TimerCallbackShouldBeInvokedAfterExactly(&timerWithRestart, 3);
    TimerCallbackShouldBeInvokedAfterExactly(&timerWithRestart, 3);
    TimerCallbackShouldBeInvokedAfterExactly(&timerWithRestart, 3);
}

static void test_ShouldNotAllowAZeroTickTimerToStarveAnotherTimer(void **state) {
    GivenThatTimerHasBeenStarted(&timer1, 3);
    GivenThatTimerHasBeenStarted(&timerWithRestart, 0);
    GivenThatTimerHasBeenStarted(&timer2, 3);
    
    // Ignore order, just verify all callbacks happen
    ignore_function_calls(Callback1);
    ignore_function_calls(CallbackWithRestart);
    ignore_function_calls(Callback2);
    will_return_always(Callback1, 0); // Disable context check
    will_return_always(Callback2, 0); // Disable context check
    
    AfterTimePassesAndTheTimerModuleIsRun(3);
}

// Export test runner function instead of main
int run_timer_tests(void) {
    const struct CMUnitTest tests[] = {
        cmocka_unit_test_setup(test_ShouldInvokeTimerCallbackOnExpiration, setup),
        cmocka_unit_test_setup(test_ShouldNotInvokeTimerCallbackAgainAfterTimerHasAlreadyExpired, setup),
        cmocka_unit_test_setup(test_ShouldAllowARunningTimerToBeRestarted, setup),
        cmocka_unit_test_setup(test_ShouldBeAbleToWatchMultipleTimers, setup),
        cmocka_unit_test_setup(test_ShouldAllowATimerToBeStoppedPriorToExpiration, setup),
        cmocka_unit_test_setup(test_ShouldExposeTheRemainingTicksForATimer, setup),
        cmocka_unit_test_setup(test_ShouldInvokeMultipleTimersPerRun, setup),
        cmocka_unit_test_setup(test_ShouldIndicateWhenRunCausesATimerToBeCalledBack, setup),
        cmocka_unit_test_setup(test_ShouldAllowATimerToBeRestartedFromACallback, setup),
        cmocka_unit_test_setup(test_ShouldNotAllowAZeroTickTimerToStarveAnotherTimer, setup),
    };

    return cmocka_run_group_tests_name("Timer Tests", tests, NULL, NULL);
}
