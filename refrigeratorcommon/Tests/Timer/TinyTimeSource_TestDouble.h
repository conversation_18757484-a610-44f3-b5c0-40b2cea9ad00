/*!
 * @file
 * @brief Time source that allows manual control of the time. Additionally, this test double allows
 * for the time to be automatically incremented on read.
 *
 * Copyright GE Appliances - Confidential - All rights reserved
 */

#ifndef TINYTIMESOURCE_TESTDOUBLE_H
#define TINYTIMESOURCE_TESTDOUBLE_H

#include <stdbool.h>
#include "TinyTimer.h"

typedef struct
{
   I_TinyTimeSource_t interface;

   struct
   {
      TinyTimeSourceTickCount_t ticks;
   } _private;
} TinyTimeSource_TestDouble_t;

void TinyTimeSource_TestDouble_Init(TinyTimeSource_TestDouble_t *instance);
void TinyTimeSource_TestDouble_SetTicks(TinyTimeSource_TestDouble_t *instance, TinyTimeSourceTickCount_t ticks);
void TinyTimeSource_TestDouble_TickOnce(TinyTimeSource_TestDouble_t *instance);
void TinyTimeSource_TestDouble_TickMany(TinyTimeSource_TestDouble_t *instance, TinyTimeSourceTickCount_t ticks);

#endif
