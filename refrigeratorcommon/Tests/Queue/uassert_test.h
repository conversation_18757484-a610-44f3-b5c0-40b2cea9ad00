/*!
 * @file
 * @brief Test utilities for handling u<PERSON><PERSON> in CMocka tests
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved.
 */

#ifndef UASSERT_TEST_H
#define UASSERT_TEST_H

#include <cmocka.h>

// Redefine uassert to use CMocka's mock_assert for testing
#ifdef uassert
#undef uassert
#endif

#define uassert(condition) \
    do { \
        if (!(condition)) { \
            mock_assert(0, #condition, __FILE__, __LINE__); \
        } \
    } while(0)

// Helper macro to test assertion failures
#define ShouldFailAssertionWhen(function_call) \
    expect_assert_failure(function_call)

#endif // UASSERT_TEST_H
