/*!
 * @file
 * @brief CMocka tests for Queue_RingBuffer module
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved.
 */

#include <stdarg.h>
#include <stddef.h>
#include <setjmp.h>
#include <cmocka.h>
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>

#include "Queue_RingBuffer.h"
#include "I_Queue.h"
#include "utils.h"
#include "uassert_test.h"

#define Given
#define Then
#define And
#define AndThen
#define When
#define Again
#define So
#define Elements

typedef struct
{
    uint8_t *element1;
    uint8_t *element2;
    uint8_t *element3;
} ThreeLargeElements_t;

enum
{
    NumberOfElements = 10,

    IndexOfFirstElement = 0,

    LargeElementSize = 25,
    SmallElementSize = 1,

    AnArbitrarySmallElement = 0x4D,
    AnotherArbitrarySmallElement = 0xC3,

    ARandomByte = 23,
    ARandomSize = 104,

    ExtraBufferingSize = 5,
    ArbitraryByte = 0xAF,

    EnoughSpaceToFitTwoLargeElements =
        LargeElementSize * 2 + sizeof(QueueElementSize_t) * 2,
    EnoughLargeElementsToFillUpStorageSpace = 2,
    EnoughSmallElementsToFillUpStorageSpace =
        EnoughSpaceToFitTwoLargeElements /
        (sizeof(QueueElementSize_t) + SmallElementSize),
    WayTooManyElements = EnoughSmallElementsToFillUpStorageSpace * 10,
};

// Test fixture data
static Queue_RingBuffer_st instance;
static uint8_t storageSpace[EnoughSpaceToFitTwoLargeElements + ExtraBufferingSize];
static uint8_t AnArbitraryLargeElement_1[LargeElementSize];
static uint8_t AnArbitraryLargeElement_2[LargeElementSize];
static uint8_t AnArbitraryLargeElement_3[LargeElementSize];

// Helper functions
static void SetTheExtraSpaceInTheSenderBufferToBeFullOfAnArbitraryValue(void)
{
    for(uint8_t index = EnoughSpaceToFitTwoLargeElements;
        index < EnoughSpaceToFitTwoLargeElements + ExtraBufferingSize;
        index++)
    {
        storageSpace[index] = ArbitraryByte;
    }
}

static void MakeSureTheExtraSpaceInTheSenderBufferWasNeverTouched(void)
{
    for(uint8_t index = EnoughSpaceToFitTwoLargeElements;
        index < EnoughSpaceToFitTwoLargeElements + ExtraBufferingSize;
        index++)
    {
        assert_int_equal(ArbitraryByte, storageSpace[index]);
    }
}

static void FillLargeElementsWithArbitraryData(void)
{
    for(uint8_t index = 0; index < LargeElementSize; index++)
    {
        memset(AnArbitraryLargeElement_1 + index, index, sizeof(uint8_t));
    }

    for(uint8_t index = 0; index < LargeElementSize; index++)
    {
        memset(AnArbitraryLargeElement_2 + index, index + 5, sizeof(uint8_t));
    }

    for(uint8_t index = 0; index < LargeElementSize; index++)
    {
        memset(AnArbitraryLargeElement_3 + index, index + 10, sizeof(uint8_t));
    }
}

static void TheQueueIsInitialized(void)
{
    Queue_RingBuffer_Init(&instance, (void *)storageSpace, sizeof(storageSpace));
}

static void ThisSmallElementIsEnqueued(uint8_t element)
{
    bool wasTheOperationSuccessful =
        Queue_Enqueue(&instance.interface, (void *)&element, sizeof(element));
    assert_true(wasTheOperationSuccessful);
}

static void ThisSmallElementShouldeBeDequeued(uint8_t element)
{
    uint8_t dequeuedElementStorage = 0;
    QueueElementSize_t sizeOfDequeuedElement = 0;
    Queue_Dequeue(&instance.interface, &dequeuedElementStorage, &sizeOfDequeuedElement);

    assert_int_equal(sizeOfDequeuedElement, sizeof(element));
    assert_int_equal(element, dequeuedElementStorage);
}

static bool ThisLargeElementIsEnqueued(uint8_t *element)
{
    return Queue_Enqueue(&instance.interface, (void *)element, LargeElementSize);
}

static void ThisManyLargeElementsAreEnqueued(uint8_t numberOfElements)
{
    for(uint8_t i = 0; i < numberOfElements; i++)
    {
        ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1);
    }
}

static void EnqueuingAnotherLargeElementShouldFail(void)
{
    assert_false(ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1));
}

static void TheDequeueOperationShouldReturn(uint8_t *element)
{
    uint8_t dequeuedElementStorage[LargeElementSize];
    memset(dequeuedElementStorage, 0, sizeof(dequeuedElementStorage));
    QueueElementSize_t sizeOfDequeuedElement = 0;

    Queue_Dequeue(&instance.interface, &dequeuedElementStorage, &sizeOfDequeuedElement);

    assert_int_equal(sizeOfDequeuedElement, LargeElementSize);
    for(uint8_t index = 0; index < LargeElementSize; index++)
    {
        assert_int_equal(element[index], dequeuedElementStorage[index]);
    }
}

static void TheDequeueOperationShouldFail(void)
{
    uint8_t randomLargeElement[LargeElementSize];
    QueueElementSize_t randomSize;

    ShouldFailAssertionWhen(
        Queue_Dequeue(&instance.interface, &randomLargeElement, &randomSize));
}

static void TheDiscardOperationShouldFail(void)
{
    ShouldFailAssertionWhen(Queue_Discard(&instance.interface));
}

static void TheDiscardOperationShouldBeSuccessfull(void)
{
    Queue_Discard(&instance.interface);
}

static void PeekingShouldResultIn(uint8_t *element, size_t sizeOfElement, uint16_t elementNumber)
{
    uint8_t peekedElementStorage[sizeOfElement];
    memset(peekedElementStorage, 0, sizeof(peekedElementStorage));
    QueueElementSize_t sizeOfDequeuedElement = 0;

    Queue_Peek(&instance.interface, &peekedElementStorage, &sizeOfDequeuedElement, elementNumber);

    assert_int_equal(sizeOfDequeuedElement, sizeOfElement);
    for(uint8_t index = 0; index < sizeOfElement; index++)
    {
        assert_int_equal(element[index], peekedElementStorage[index]);
    }
}

static void PeekingFromTheFrontOfTheQueueShouldResultIn(uint8_t *element)
{
    PeekingShouldResultIn(element, LargeElementSize, IndexOfFirstElement);
}

static void PeekingFromTheFrontOfTheQueueShouldFail(void)
{
    uint8_t element;
    QueueElementSize_t size;
    ShouldFailAssertionWhen(
        Queue_Peek(&instance.interface, &element, &size, IndexOfFirstElement));
}

static void PeekingSizeOfIndexShouldYield(uint16_t index, QueueElementSize_t expected)
{
    QueueElementSize_t actual;
    Queue_PeekSize(&instance.interface, &actual, index);
    assert_int_equal(expected, actual);
}

static void ShouldBeUnableToPeekSizeOfIndex(uint16_t index)
{
    QueueElementSize_t size;
    ShouldFailAssertionWhen(Queue_PeekSize(&instance.interface, &size, index));
}

static void ShouldBeAbleToPeekNBytesOf(uint16_t index, QueueElementSize_t partialSize)
{
    uint8_t full[LargeElementSize];
    uint8_t partial[LargeElementSize];
    uint8_t empty[LargeElementSize + 1];
    QueueElementSize_t fullSize;

    memset(partial, 0xA5, sizeof(partial));
    memset(empty, 0xA5, sizeof(empty));

    Queue_Peek(&instance.interface, full, &fullSize, index);
    Queue_PeekPartial(&instance.interface, partial, partialSize, index);

    assert_memory_equal(full, partial, partialSize);
    assert_memory_equal(empty + partialSize, partial + partialSize, sizeof(partial) - partialSize);
    assert_int_equal(0xA5, empty[sizeof(empty) - 1]);
}

// Setup function called before each test
static int setup(void **state)
{
    SetTheExtraSpaceInTheSenderBufferToBeFullOfAnArbitraryValue();
    FillLargeElementsWithArbitraryData();
    return 0;
}

// Teardown function called after each test
static int teardown(void **state)
{
    MakeSureTheExtraSpaceInTheSenderBufferWasNeverTouched();
    return 0;
}

static void EnqueueOneByteElementsSuchThatEachElementIsIncremented(
    uint8_t startingNumber, uint8_t numberOfElements)
{
    uint8_t element = startingNumber;
    for(uint8_t count = 0; count < numberOfElements; count++)
    {
        Queue_Enqueue(&instance.interface, &element, sizeof(element));
        element++;
    }
}

static void Enqueue(uint16_t numberOfElements)
{
    EnqueueOneByteElementsSuchThatEachElementIsIncremented(0, numberOfElements);
}

static void TheQueueIsFilledUpWithElements(void)
{
    EnqueueOneByteElementsSuchThatEachElementIsIncremented(
        45, EnoughSmallElementsToFillUpStorageSpace);
}

static void ThePeekOperationShouldBeAbleToPeekEveryElementInTheQueue(void)
{
    uint8_t element = 45;
    for(uint8_t index = 0; index < EnoughSmallElementsToFillUpStorageSpace; index++)
    {
        PeekingShouldResultIn(&element, sizeof(element), index);
        element++;
    }
}

static void Dequeue(uint8_t numberOfArbitraryElementsToDequeue)
{
    uint8_t element = 0;
    QueueElementSize_t size = 0;
    for(uint8_t count = 0; count < numberOfArbitraryElementsToDequeue; count++)
    {
        Queue_Dequeue(&instance.interface, &element, &size);
    }
}

static void Discard(uint8_t numberOfArbitraryElementsToDequeue)
{
    for(uint8_t count = 0; count < numberOfArbitraryElementsToDequeue; count++)
    {
        Queue_Discard(&instance.interface);
    }
}

static void CountOfElementsShouldBe(uint8_t expectedNumberOfElements)
{
    assert_int_equal(expectedNumberOfElements, Queue_Count(&instance.interface));
}

// Test cases
static void test_ShouldBeAbleToEnqueueAndDequeueASmallElement(void **state)
{
    Given TheQueueIsInitialized();
    And ThisSmallElementIsEnqueued(AnArbitrarySmallElement);

    ThisSmallElementShouldeBeDequeued(AnArbitrarySmallElement);
}

static void test_ShouldBeAbleToEnqueueAndDiscardASmallElement(void **state)
{
    Given TheQueueIsInitialized();
    And ThisSmallElementIsEnqueued(AnArbitrarySmallElement);

    TheDiscardOperationShouldBeSuccessfull();
}

static void test_ShouldBeAbleToEnqueueAndDequeueALargeElement(void **state)
{
    Given TheQueueIsInitialized();
    And ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1);

    TheDequeueOperationShouldReturn(AnArbitraryLargeElement_1);
}

static void test_ShouldBeAbleToEnqueueThreeElementsAndThenDequeueThem(void **state)
{
    Given TheQueueIsInitialized();

    ThisSmallElementIsEnqueued(AnArbitrarySmallElement);
    Then ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1);
    And ThisSmallElementIsEnqueued(AnotherArbitrarySmallElement);

    So ThisSmallElementShouldeBeDequeued(AnArbitrarySmallElement);
    And TheDequeueOperationShouldReturn(AnArbitraryLargeElement_1);
    And ThisSmallElementShouldeBeDequeued(AnotherArbitrarySmallElement);
}

static void test_ShouldBeAbleToDiscardSomeElementsAndThenDequeueOthers(void **state)
{
    Given TheQueueIsInitialized();

    ThisSmallElementIsEnqueued(AnArbitrarySmallElement);
    Then ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1);
    And ThisSmallElementIsEnqueued(AnotherArbitrarySmallElement);

    Then TheDiscardOperationShouldBeSuccessfull();
    And TheDequeueOperationShouldReturn(AnArbitraryLargeElement_1);
    And ThisSmallElementShouldeBeDequeued(AnotherArbitrarySmallElement);
}

static void test_ShouldBeAbleToEnqueueMoreElementsAfterDequeuingAndDiscarding(void **state)
{
    Given TheQueueIsInitialized();

    ThisSmallElementIsEnqueued(AnArbitrarySmallElement);
    Then ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1);

    Then TheDiscardOperationShouldBeSuccessfull();
    And TheDequeueOperationShouldReturn(AnArbitraryLargeElement_1);

    ThisSmallElementIsEnqueued(AnArbitrarySmallElement);
    And ThisSmallElementShouldeBeDequeued(AnArbitrarySmallElement);
}

static void test_ShouldNotEnqueueElementsThatWillNotFitInTheQueue(void **state)
{
    Given TheQueueIsInitialized();
    And ThisManyLargeElementsAreEnqueued(EnoughLargeElementsToFillUpStorageSpace);
    EnqueuingAnotherLargeElementShouldFail();
}

static void test_ShouldBeAbleToPeekAtAnElementMultipleTimes(void **state)
{
    Given TheQueueIsInitialized();
    And ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1);
    And ThisLargeElementIsEnqueued(AnArbitraryLargeElement_2);

    PeekingFromTheFrontOfTheQueueShouldResultIn(AnArbitraryLargeElement_1);
    And Again PeekingFromTheFrontOfTheQueueShouldResultIn(AnArbitraryLargeElement_1);
    And Again PeekingFromTheFrontOfTheQueueShouldResultIn(AnArbitraryLargeElement_1);
}

static void test_ShouldBeAbleToPeekAtAnyElementThatIsQueued(void **state)
{
    Given TheQueueIsInitialized();
    TheQueueIsFilledUpWithElements();
    ThePeekOperationShouldBeAbleToPeekEveryElementInTheQueue();
}

static void test_ShouldNotBeAbleToPeekAtAnElementThatIsNotQueued(void **state)
{
    Given TheQueueIsInitialized();
    PeekingFromTheFrontOfTheQueueShouldFail();
}

static void test_ShouldBeAbleToPeekAtAnElementAndThenDequeueIt(void **state)
{
    Given TheQueueIsInitialized();
    And ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1);

    PeekingFromTheFrontOfTheQueueShouldResultIn(AnArbitraryLargeElement_1);
    And TheDequeueOperationShouldReturn(AnArbitraryLargeElement_1);
}

static void test_ShouldBeAbleToPeekJustPartOfAnElement(void **state)
{
    Given TheQueueIsInitialized();
    And ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1);

    ShouldBeAbleToPeekNBytesOf(0, 1);
    ShouldBeAbleToPeekNBytesOf(0, 10);
    ShouldBeAbleToPeekNBytesOf(0, 15);
    ShouldBeAbleToPeekNBytesOf(0, LargeElementSize);
}

static void test_ShouldBeAbleToPeekSizeOfAnElementMultipleTimes(void **state)
{
    Given TheQueueIsInitialized();
    And ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1);
    And ThisLargeElementIsEnqueued(AnArbitraryLargeElement_2);

    PeekingSizeOfIndexShouldYield(0, LargeElementSize);
    And Again PeekingSizeOfIndexShouldYield(0, LargeElementSize);
    And Again PeekingSizeOfIndexShouldYield(0, LargeElementSize);
}

static void test_ShouldBeAbleToPeekSizeOfAnyElementThatIsQueued(void **state)
{
    Given TheQueueIsInitialized();
    And ThisLargeElementIsEnqueued(AnArbitraryLargeElement_1);
    And ThisSmallElementIsEnqueued(21);
    And ThisSmallElementIsEnqueued(5);

    PeekingSizeOfIndexShouldYield(0, LargeElementSize);
    PeekingSizeOfIndexShouldYield(1, SmallElementSize);
    PeekingSizeOfIndexShouldYield(2, SmallElementSize);
}

static void test_ShouldNotBeAbleToPeekSizeOfAnElementThatIsNotQueued(void **state)
{
    Given TheQueueIsInitialized();
    ShouldBeUnableToPeekSizeOfIndex(0);
    ShouldBeUnableToPeekSizeOfIndex(1);
    ShouldBeUnableToPeekSizeOfIndex(2);
}

static void test_ShouldNotBeAbleToDequeueAnElementIfTheQueueIsEmpty(void **state)
{
    Given TheQueueIsInitialized();
    TheDequeueOperationShouldFail();
}

static void test_ShouldNotBeAbleToDiscardAnElementIfTheQueueIsEmpty(void **state)
{
    Given TheQueueIsInitialized();
    TheDiscardOperationShouldFail();
}

static void test_ShouldKnowHowManyElementsAreQueued(void **state)
{
    TheQueueIsInitialized();
    Enqueue(4 Elements);

    So CountOfElementsShouldBe(4);
}

static void test_ShouldKnowHowManyElementsAreQueuedAfterSomeHaveBeenDequeued(void **state)
{
    TheQueueIsInitialized();
    Enqueue(4 Elements);
    Dequeue(2 Elements);

    So CountOfElementsShouldBe(2);
}

static void test_ShouldKnowHowManyElementsAreQueuedAfterSomeHaveBeenDiscarded(void **state)
{
    TheQueueIsInitialized();
    Enqueue(4 Elements);
    Discard(2 Elements);

    So CountOfElementsShouldBe(2);
}

// Test runner function
int run_queue_ring_buffer_tests(void)
{
    const struct CMUnitTest tests[] = {
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToEnqueueAndDequeueASmallElement, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToEnqueueAndDiscardASmallElement, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToEnqueueAndDequeueALargeElement, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToEnqueueThreeElementsAndThenDequeueThem, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToDiscardSomeElementsAndThenDequeueOthers, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToEnqueueMoreElementsAfterDequeuingAndDiscarding, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldNotEnqueueElementsThatWillNotFitInTheQueue, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToPeekAtAnElementMultipleTimes, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToPeekAtAnyElementThatIsQueued, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldNotBeAbleToPeekAtAnElementThatIsNotQueued, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToPeekAtAnElementAndThenDequeueIt, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToPeekJustPartOfAnElement, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToPeekSizeOfAnElementMultipleTimes, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldBeAbleToPeekSizeOfAnyElementThatIsQueued, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldNotBeAbleToPeekSizeOfAnElementThatIsNotQueued, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldNotBeAbleToDequeueAnElementIfTheQueueIsEmpty, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldNotBeAbleToDiscardAnElementIfTheQueueIsEmpty, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldKnowHowManyElementsAreQueued, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldKnowHowManyElementsAreQueuedAfterSomeHaveBeenDequeued, setup, teardown),
        cmocka_unit_test_setup_teardown(test_ShouldKnowHowManyElementsAreQueuedAfterSomeHaveBeenDiscarded, setup, teardown),
    };

    return cmocka_run_group_tests_name("Queue_RingBuffer Tests", tests, NULL, NULL);
}
