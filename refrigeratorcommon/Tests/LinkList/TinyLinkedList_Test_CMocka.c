/*!
 * @file
 * @brief CMocka tests for TinyLinkedList
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <stdarg.h>
#include <stddef.h>
#include <setjmp.h>
#include <cmocka.h>
#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>

#include "TinyLinkedList.h"
#include "utils.h"

// Test fixture data
static TinyLinkedList_t list;
static TinyLinkedListNode_t node1, node2, node3;
static uint8_t context;

// Mock function for ForEach testing
static void mock_for_each_callback(TinyLinkedListNode_t *node, void *ctx)
{
    check_expected_ptr(node);
    check_expected_ptr(ctx);
}

// Setup function called before each test
static int setup(void **state)
{
    TinyLinkedList_Init(&list);
    return 0;
}

// Helper functions
static void insert_node(TinyLinkedListNode_t *node)
{
    TinyLinkedList_Insert(&list, node);
}

static void remove_node(TinyLinkedListNode_t *node)
{
    TinyLinkedList_Remove(&list, node);
}

// Helper to verify list contents
static void verify_list_contains(TinyLinkedListNode_t **expected_nodes, size_t count)
{
    size_t actual_count = 0;
    TinyLinkedListNode_t *actual_nodes[10]; // Assume max 10 nodes for testing

    TinyLinkedList_ForEach(&list, TinyLinkedListNode_t, node, {
        actual_nodes[actual_count++] = node;
    });

    assert_int_equal(actual_count, count);

    for(size_t i = 0; i < count; i++)
    {
        assert_ptr_equal(actual_nodes[i], expected_nodes[i]);
    }
}

static void verify_list_empty(void)
{
    verify_list_contains(NULL, 0);
}

// Test cases
static void test_should_be_empty_after_init(void **state)
{
    verify_list_empty();
}

static void test_should_be_able_to_insert_a_node(void **state)
{
    insert_node(&node1);

    TinyLinkedListNode_t *expected[] = { &node1 };
    verify_list_contains(expected, 1);
}

static void test_should_be_able_to_insert_multiple_nodes(void **state)
{
    insert_node(&node1);
    insert_node(&node2);
    insert_node(&node3);

    // TinyLinkedList inserts at head, so order is reversed
    TinyLinkedListNode_t *expected[] = { &node3, &node2, &node1 };
    verify_list_contains(expected, 3);
}

static void test_should_not_explode_when_removing_node_not_in_list(void **state)
{
    remove_node(&node1);
    verify_list_empty();
}

static void test_should_allow_first_node_to_be_removed(void **state)
{
    insert_node(&node1);
    insert_node(&node2);
    remove_node(&node2); // node2 is first (head)

    TinyLinkedListNode_t *expected[] = { &node1 };
    verify_list_contains(expected, 1);
}

static void test_should_allow_last_node_to_be_removed(void **state)
{
    insert_node(&node1);
    insert_node(&node2);
    remove_node(&node1); // node1 is last

    TinyLinkedListNode_t *expected[] = { &node2 };
    verify_list_contains(expected, 1);
}

static void test_should_allow_middle_node_to_be_removed(void **state)
{
    insert_node(&node1);
    insert_node(&node2);
    insert_node(&node3);
    remove_node(&node2); // node2 is in middle

    TinyLinkedListNode_t *expected[] = { &node3, &node1 };
    verify_list_contains(expected, 2);
}

static void test_should_be_empty_after_removing_all_nodes(void **state)
{
    insert_node(&node1);
    insert_node(&node2);
    insert_node(&node3);

    remove_node(&node1);
    remove_node(&node2);
    remove_node(&node3);

    verify_list_empty();
}

static void test_should_iterate_through_all_nodes_in_order_with_foreach(void **state)
{
    TinyLinkedList_Insert(&list, &node3);
    TinyLinkedList_Insert(&list, &node2);
    TinyLinkedList_Insert(&list, &node1);

    // Expect nodes to be visited in insertion order (head first)
    expect_value(mock_for_each_callback, node, &node1);
    expect_value(mock_for_each_callback, ctx, &context);
    expect_value(mock_for_each_callback, node, &node2);
    expect_value(mock_for_each_callback, ctx, &context);
    expect_value(mock_for_each_callback, node, &node3);
    expect_value(mock_for_each_callback, ctx, &context);

    TinyLinkedList_ForEach(&list, TinyLinkedListNode_t, node, {
        mock_for_each_callback(node, &context);
    });
}

static void test_should_allow_current_node_to_be_removed_during_iteration(void **state)
{
    TinyLinkedList_Insert(&list, &node3);
    TinyLinkedList_Insert(&list, &node2);
    TinyLinkedList_Insert(&list, &node1);

    // Expect all nodes to be visited even if one is removed
    expect_value(mock_for_each_callback, node, &node1);
    expect_value(mock_for_each_callback, ctx, &context);
    expect_value(mock_for_each_callback, node, &node2);
    expect_value(mock_for_each_callback, ctx, &context);
    expect_value(mock_for_each_callback, node, &node3);
    expect_value(mock_for_each_callback, ctx, &context);

    TinyLinkedList_ForEach(&list, TinyLinkedListNode_t, node, {
        if(node == &node1)
        {
            TinyLinkedList_Remove(&list, node);
        }
        mock_for_each_callback(node, &context);
    });
}

// Test runner function
int run_tiny_linked_list_tests(void)
{
    const struct CMUnitTest tests[] = {
        cmocka_unit_test_setup(test_should_be_empty_after_init, setup),
        cmocka_unit_test_setup(test_should_be_able_to_insert_a_node, setup),
        cmocka_unit_test_setup(test_should_be_able_to_insert_multiple_nodes, setup),
        cmocka_unit_test_setup(test_should_not_explode_when_removing_node_not_in_list, setup),
        cmocka_unit_test_setup(test_should_allow_first_node_to_be_removed, setup),
        cmocka_unit_test_setup(test_should_allow_last_node_to_be_removed, setup),
        cmocka_unit_test_setup(test_should_allow_middle_node_to_be_removed, setup),
        cmocka_unit_test_setup(test_should_be_empty_after_removing_all_nodes, setup),
        cmocka_unit_test_setup(test_should_iterate_through_all_nodes_in_order_with_foreach, setup),
        cmocka_unit_test_setup(test_should_allow_current_node_to_be_removed_during_iteration, setup),
    };

    return cmocka_run_group_tests(tests, NULL, NULL);
}